import tensorflow as tf
import numpy as np
import matplotlib.pyplot as plt
from tensorflow.keras.preprocessing.image import load_img, img_to_array

# === Step 1: Load and preprocess image ===
def load_grayscale_image(img_path, target_size=(256, 256)):
    img = load_img(img_path, target_size=target_size, color_mode='grayscale')
    img_array = img_to_array(img) / 255.0  # Normalize to [0, 1]
    img_array = np.expand_dims(img_array, axis=0)  # Shape: (1, H, W, 1)
    return img_array

# === Step 2: Define Edge Detection Kernel ===
def get_edge_filter(filter_type='horizontal'):
    if filter_type == 'horizontal':
        kernel = np.array([[-1, -1, -1],
                           [ 0,  0,  0],
                           [ 1,  1,  1]], dtype=np.float32)
    elif filter_type == 'vertical':
        kernel = np.array([[-1, 0, 1],
                           [-1, 0, 1],
                           [-1, 0, 1]], dtype=np.float32)
    elif filter_type == 'sobel_x':
        kernel = np.array([[-1, 0, 1],
                           [-2, 0, 2],
                           [-1, 0, 1]], dtype=np.float32)
    elif filter_type == 'sobel_y':
        kernel = np.array([[-1, -2, -1],
                           [ 0,  0,  0],
                           [ 1,  2,  1]], dtype=np.float32)
    else:
        raise ValueError("Unknown filter_type.")
    
    return kernel.reshape((3, 3, 1, 1))

# === Step 3: Build Conv2D Layer with Fixed Weights ===
def apply_edge_filter(img_array, kernel):
    edge_layer = tf.keras.layers.Conv2D(filters=1,
                                        kernel_size=(3, 3),
                                        strides=(1, 1),
                                        padding='same',
                                        use_bias=False,
                                        trainable=False)
    
    edge_layer.build(input_shape=(None, img_array.shape[1], img_array.shape[2], 1))
    edge_layer.set_weights([kernel])
    
    output = edge_layer(img_array)
    return output

# === Step 4: Plot Original and Edge-detected Images ===
def plot_results(original, edge_detected):
    plt.figure(figsize=(12, 5))

    plt.subplot(1, 2, 1)
    plt.title("Original")
    plt.imshow(original[0, :, :, 0], cmap='gray')
    plt.axis('off')

    plt.subplot(1, 2, 2)
    plt.title("Edge Detected")
    plt.imshow(edge_detected[0, :, :, 0], cmap='gray')
    plt.axis('off')

    plt.show()

# === Run Everything ===
if __name__ == '__main__':
    image_path = 'your_image.jpg'  # Replace with your actual image path
    img_array = load_grayscale_image(image_path)
    
    kernel = get_edge_filter(filter_type='sobel_x')  # Try 'horizontal', 'vertical', 'sobel_x', 'sobel_y'
    edge_detected = apply_edge_filter(img_array, kernel)
    
    plot_results(img_array, edge_detected)
